import {
  createLocalAudioTrack,
  createLocalTracks,
  createLocalVideoTrack,
  facingModeFromLocalTrack,
  Track,
  VideoPresets,
} from "livekit-client";
import * as React from "react";
import { Modal } from "antd";
import { MdExpandMore } from "react-icons/md";
import {
  // MediaDeviceMenu,
  // TrackToggle,
  useMediaDevices,
  usePersistentUserChoices,
} from "@livekit/components-react";
import { onDeviceError } from "../utils/helper";
import { ParticipantPlaceholder } from "../assets/images/index";
import { TrackToggle } from "../components/TrackToggle";
import "../styles/Prejoin.scss";
// import AudioVideoSettingsModal from "../components/AudioVideoSettingsModal/AudioVideoSettingsModal";
// import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
import AudioSettings from "../components/AudioVideoSettingsModal/AudioSettings";
import VideoSettings from "../components/AudioVideoSettingsModal/VideoSettings";
import PermissionUi from "../components/permissionUi/permissionUi";

/**
 * @public
 */
export function usePreviewTracks(options, onError, setToastMessage, setToastStatus, setShowToast) {
  const [tracks, setTracks] = React.useState();
  const [showPermissionModal, setShowPermissionModal] = React.useState(false);
  const [permissionType, setPermissionType] = React.useState('both');
  const [permissionState, setPermissionState] = React.useState({
    camera: 'unknown',
    microphone: 'unknown'
  });

  // Check if permissions are already granted
  const checkExistingPermissions = async () => {
    try {
      // Check if navigator.permissions is supported
      if (navigator.permissions && navigator.permissions.query) {
        // Check camera permission
        if (options.video) {
          const cameraResult = await navigator.permissions.query({ name: 'camera' });
          setPermissionState(prev => ({ ...prev, camera: cameraResult.state }));
        }

        // Check microphone permission
        if (options.audio) {
          const micResult = await navigator.permissions.query({ name: 'microphone' });
          setPermissionState(prev => ({ ...prev, microphone: micResult.state }));
        }
      }
    } catch (error) {
      console.log("Error checking permissions:", error);
      // If we can't check permissions, we'll show the modal anyway
    }
  };

  const handlePermissionResponse = async (response) => {
    setShowPermissionModal(false);
    let needsCleanup = false;
    let localTracks = [];

    try {
      if (response === 'skip') {
        setTracks([]);
        return;
      }

      const trackOptions = {
        audio: response.includes('mic') ? { deviceId: options.audio?.deviceId } : false,
        video: response.includes('camera') ? { deviceId: options.video?.deviceId } : false,
      };

      localTracks = await createLocalTracks(trackOptions);

      if (needsCleanup) {
        localTracks.forEach((tr) => tr.stop());
      } else {
        setTracks(localTracks);

        // Update permission state after successful track creation
        if (trackOptions.video) {
          setPermissionState(prev => ({ ...prev, camera: 'granted' }));
        }
        if (trackOptions.audio) {
          setPermissionState(prev => ({ ...prev, microphone: 'granted' }));
        }
      }
    } catch (e) {
      console.log("Permission error:", e);

      // Determine which permission was denied
      let errorMessage = "Permissions denied for accessing audio/video devices.";
      let deniedPermission = '';

      if (e.message && e.message.toLowerCase().includes('video')) {
        errorMessage = "Camera access was denied. Please check your browser settings.";
        deniedPermission = 'camera';
      } else if (e.message && e.message.toLowerCase().includes('audio')) {
        errorMessage = "Microphone access was denied. Please check your browser settings.";
        deniedPermission = 'microphone';
      }

      // Update permission state for the denied device
      if (deniedPermission) {
        setPermissionState(prev => ({ ...prev, [deniedPermission]: 'denied' }));
      }

      if (onError && e instanceof Error) {
        onError(e);
      } else {
        setToastMessage(errorMessage);
        setToastStatus("error");
        setShowToast(true);
      }
    }
  };

  // Helper function to check if we should show the permission modal
  const shouldShowPermissionModal = () => {
    // If no permissions are requested, don't show modal
    if (!options.audio && !options.video) {
      return false;
    }

    // If any requested permission is denied, show modal with guidance
    if ((options.audio && permissionState.microphone === 'denied') ||
        (options.video && permissionState.camera === 'denied')) {
      return true;
    }

    // If all requested permissions are already granted, don't show modal
    if ((!options.audio || permissionState.microphone === 'granted') &&
        (!options.video || permissionState.camera === 'granted')) {
      return false;
    }

    // In all other cases (unknown, prompt), show the modal
    return true;
  };

  React.useEffect(() => {
    if (options.audio || options.video) {
      // First check existing permissions
      checkExistingPermissions().then(() => {
        // Determine permission type based on options
        const newPermissionType = options.audio && options.video ? 'both' : options.audio ? 'mic' : 'camera';
        setPermissionType(newPermissionType);

        // Only show permission modal if needed
        if (shouldShowPermissionModal()) {
          setShowPermissionModal(true);
        } else if (permissionState.camera === 'granted' || permissionState.microphone === 'granted') {
          // If permissions are already granted, create tracks directly
          const trackOptions = {
            audio: options.audio && permissionState.microphone === 'granted' ? options.audio : false,
            video: options.video && permissionState.camera === 'granted' ? options.video : false,
          };

          createLocalTracks(trackOptions)
            .then(localTracks => {
              setTracks(localTracks);
            })
            .catch(e => {
              console.log("Error creating tracks:", e);
              // Show permission modal if there was an error
              setShowPermissionModal(true);
            });
        }
      });
    }
  }, [JSON.stringify(options)]);

  return {
    tracks,
    showPermissionModal,
    setShowPermissionModal,
    permissionType,
    permissionState,
    handlePermissionResponse
  };
}

/** @public */
export function usePreviewDevice(enabled, deviceId, deviceKind) {
  const [deviceError, setDeviceError] = React.useState(null);
  const [isCreatingTrack, setIsCreatingTrack] = React.useState(false);

  const devices = useMediaDevices({ kind: deviceKind });
  const [selectedDevice, setSelectedDevice] = React.useState(undefined);

  const [localTrack, setLocalTrack] = React.useState();
  const [localDeviceId, setLocalDeviceId] = React.useState(deviceId);

  React.useEffect(() => {
    setLocalDeviceId(deviceId);
  }, [deviceId]);

  const prevDeviceId = React.useRef(localDeviceId); // Moved prevDeviceId declaration here

  const createTrack = async (id, kind) => {
    try {
      const track =
        kind === "videoinput"
          ? await createLocalVideoTrack({
              deviceId: id,
              resolution: VideoPresets.h720.resolution,
            })
          : await createLocalAudioTrack({ deviceId: id });

      const newDeviceId = await track.getDeviceId();
      if (newDeviceId && id !== newDeviceId) {
        prevDeviceId.current = newDeviceId;
        setLocalDeviceId(newDeviceId);
      }
      setLocalTrack(track);
    } catch (e) {
      if (e instanceof Error) {
        setDeviceError(e);
      }
    }
  };

  const switchDevice = async (track, id) => {
    await track.setDeviceId(id);
    prevDeviceId.current = id;
  };

  React.useEffect(() => {
    if (enabled && !localTrack && !deviceError && !isCreatingTrack) {
      setIsCreatingTrack(true);
      createTrack(localDeviceId, deviceKind).finally(() => {
        setIsCreatingTrack(false);
      });
    }
  }, [
    enabled,
    localTrack,
    deviceError,
    isCreatingTrack,
    localDeviceId,
    deviceKind,
  ]);

  React.useEffect(() => {
    if (!localTrack) {
      return;
    }
    if (!enabled) {
      localTrack.mute().then(() => console.log(localTrack.mediaStreamTrack));
    } else if (
      selectedDevice?.deviceId &&
      prevDeviceId.current !== selectedDevice?.deviceId
    ) {
      switchDevice(localTrack, selectedDevice.deviceId);
    } else {
      localTrack.unmute();
    }
  }, [localTrack, selectedDevice, enabled, deviceKind]);

  React.useEffect(() => {
    return () => {
      if (localTrack) {
        localTrack.stop();
        localTrack.mute();
      }
    };
  }, [localTrack]);

  React.useEffect(() => {
    setSelectedDevice(devices?.find((dev) => dev.deviceId === localDeviceId));
  }, [localDeviceId, devices]);

  return {
    selectedDevice,
    localTrack,
    deviceError,
  };
}

export function PreJoinAudioVideo({
  defaults = {},
  onValidate,
  onError,
  username,
  setIsValid,
  setUserChoices,
  persistUserChoices = true,
  setToastMessage,
  setToastStatus,
  setShowToast,
  setToastNotification,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setDeviceIdAudio,
  ...htmlProps
}) {
  const [isAudioModalOpen, setIsAudioModalOpen] = React.useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = React.useState(false);
  // const [isVisualEffectsModalOpen, setIsVisualEffectsModalOpen] = React.useState(false);

  const partialDefaults = {
    ...(defaults.audioDeviceId !== undefined && {
      audioDeviceId: defaults.audioDeviceId,
    }),
    ...(defaults.videoDeviceId !== undefined && {
      videoDeviceId: defaults.videoDeviceId,
    }),
    ...(defaults.audioEnabled !== undefined && {
      audioEnabled: defaults.audioEnabled,
    }),
    ...(defaults.videoEnabled !== undefined && {
      videoEnabled: defaults.videoEnabled,
    }),
    ...(defaults.username !== undefined && { username: defaults.username }),
  };

  const {
    userChoices: initialUserChoices,
    saveAudioInputDeviceId,
    saveAudioInputEnabled,
    saveVideoInputDeviceId,
    saveVideoInputEnabled,
    saveUsername,
  } = usePersistentUserChoices({
    defaults: partialDefaults,
    preventSave: !persistUserChoices,
    preventLoad: !persistUserChoices,
  });

  const [audioEnabled, setAudioEnabled] = React.useState(
    initialUserChoices.audioEnabled
  );
  const [videoEnabled, setVideoEnabled] = React.useState(
    initialUserChoices.videoEnabled
  );
  const [audioDeviceId, setAudioDeviceId] = React.useState(
    initialUserChoices.audioDeviceId
  );
  const [videoDeviceId, setVideoDeviceId] = React.useState(
    initialUserChoices.videoDeviceId
  );
  const [speakerDeviceId, setSpeakerDeviceId] = React.useState("");

  React.useEffect(() => {
    saveAudioInputEnabled(audioEnabled);
  }, [audioEnabled, saveAudioInputEnabled]);
  React.useEffect(() => {
    saveVideoInputEnabled(videoEnabled);
  }, [videoEnabled, saveVideoInputEnabled]);
  React.useEffect(() => {
    saveAudioInputDeviceId(audioDeviceId);
  }, [audioDeviceId, saveAudioInputDeviceId]);
  React.useEffect(() => {
    saveVideoInputDeviceId(videoDeviceId);
  }, [videoDeviceId, saveVideoInputDeviceId]);
  React.useEffect(() => {
    saveUsername(username);
  }, [username, saveUsername]);

  const {
    tracks,
    showPermissionModal,
    setShowPermissionModal,
    permissionType,
    permissionState,
    handlePermissionResponse
  } = usePreviewTracks(
    {
      audio: audioEnabled
        ? { deviceId: initialUserChoices.audioDeviceId }
        : false,
      video: videoEnabled
        ? { deviceId: initialUserChoices.videoDeviceId }
        : false,
    },
    onError,
    setToastMessage,
    setToastStatus,
    setShowToast
  );

  const videoEl = React.useRef(null);

  const videoTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Video)[0],
    [tracks]
  );

  const trackFacingMode = React.useMemo(() => {
    if (videoTrack) {
      const { facingMode } = facingModeFromLocalTrack(videoTrack);
      return facingMode;
    } else {
      return "undefined";
    }
  }, [videoTrack]);

  const audioTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Audio)[0],
    [tracks]
  );

  React.useEffect(() => {
    if (videoEl.current && videoTrack) {
      videoTrack.unmute();
      videoTrack.attach(videoEl.current);
    }

    return () => {
      videoTrack?.detach();
    };
  }, [videoTrack]);

  const handleValidation = React.useCallback(
    (values) => {
      if (typeof onValidate === "function") {
        return onValidate(values);
      } else {
        return values.username !== "";
      }
    },
    [onValidate]
  );

  React.useEffect(() => {
    const newUserChoices = {
      username,
      videoEnabled,
      videoDeviceId,
      audioEnabled,
      audioDeviceId,
    };
    setUserChoices(newUserChoices);
    setIsValid(handleValidation(newUserChoices));
  }, [
    username,
    videoEnabled,
    videoDeviceId,
    audioEnabled,
    audioDeviceId,
    handleValidation,
  ]);

  React.useEffect(() => {
    setAudioDeviceId(audioDeviceId);
    setDeviceIdAudio(audioDeviceId);
    setVideoDeviceId(videoDeviceId);
  }, [
    audioDeviceId,
    videoTrack,
    videoDeviceId,
    isVideoModalOpen
  ]);
  return (
    <div className="lk-prejoin" {...htmlProps}>
      <div className="lk-video-container">
        {videoTrack && (
          <video
            ref={videoEl}
            width="1280"
            height="720"
            data-lk-facing-mode={trackFacingMode}
            className={isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}
          >
            <track kind="captions" />
          </video>
        )}
        {(!videoTrack || !videoEnabled) && (
          <div className="lk-camera-off-note">
            <ParticipantPlaceholder />
          </div>
        )}
      </div>
      <div
        className="lk-button-group-container"
        style={{ gap: "3rem", justifyContent: "center" }}
      >
        <div
          className="lk-button-group audio"
          style={{ width: "auto", justifyContent: "center" }}
        >
          <TrackToggle
            initialState={audioEnabled}
            source={Track.Source.Microphone}
            showIcon
            onChange={(enabled) => setAudioEnabled(enabled)}
            onDeviceError={onDeviceError}
            className="button-icon"
          />
          <div
            className="lk-button-group-menu"
            onClick={() => {
              setIsAudioModalOpen(!isAudioModalOpen);
              setIsVideoModalOpen(false);
            }}
          >
            <MdExpandMore />
          </div>
        </div>
        <div
          className="lk-button-group video"
          style={{ width: "auto", justifyContent: "center" }}
          >
          <TrackToggle
            initialState={videoEnabled}
            source={Track.Source.Camera}
            showIcon
            onChange={(enabled) => setVideoEnabled(enabled)}
            onDeviceError={onDeviceError}
            className="button-icon"
          />
          <div
            className="lk-button-group-menu"
            onClick={() => {
              setIsAudioModalOpen(false);
              setIsVideoModalOpen(!isVideoModalOpen);
            }}
          >
            <MdExpandMore />
          </div>
        </div>
      </div>

      {/* Audio Video Settings Modal */}
      {/* <AudioVideoSettingsModal
        audio={isAudioModalOpen}
        video={isVideoModalOpen}
        open={isAudioModalOpen || isVideoModalOpen}
        setOpen={isAudioModalOpen ? setIsAudioModalOpen : setIsVideoModalOpen}
        deviceId={isAudioModalOpen ? audioDeviceId : videoDeviceId}
        setDeviceId={isAudioModalOpen ? setAudioDeviceId : setVideoDeviceId}
        track={isAudioModalOpen ? audioTrack : videoTrack}
        setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
        isNoiseCancellationActive={isNoiseCancellationActive}
        setIsNoiseCancellationActive={setIsNoiseCancellationActive}
        isEchoCancellationActive={isEchoCancellationActive}
        setIsEchoCancellationActive={setIsEchoCancellationActive}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      /> */}

      {/* Audio Settings Modal */}
      <Modal
        open={isAudioModalOpen}
        onCancel={() => setIsAudioModalOpen(false)}
        footer={null}
      >
        <AudioSettings
          deviceId={audioDeviceId}
          setDeviceId={setAudioDeviceId}
          track={audioTrack}
          speakerDeviceId={speakerDeviceId}
          setSpeakerDeviceId={setSpeakerDeviceId}
          room={room}
        />
      </Modal>

      {/* Video Settings Modal */}
      <Modal
        open={isVideoModalOpen}
        onCancel={() => setIsVideoModalOpen(false)}
        footer={null}
      >
        <VideoSettings
          deviceId={videoDeviceId}
          setDeviceId={setVideoDeviceId}
          track={videoTrack}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          // setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
          open={isVideoModalOpen}
          trackFacingMode={trackFacingMode}
        />
      </Modal>

      {/* Virtual Background Modal */}
      {/* <VirtualBackgroundModal
        open={isVisualEffectsModalOpen}
        setOpen={setIsVisualEffectsModalOpen}
        backgrounds={backgrounds}
        setBackgrounds={setBackgrounds}
        room={room}
      /> */}

      <PermissionUi
        open={showPermissionModal}
        onClose={() => setShowPermissionModal(false)}
        onAllow={handlePermissionResponse}
        permissionType={permissionType}
        permissionState={permissionState}
      />
    </div>
  );
}