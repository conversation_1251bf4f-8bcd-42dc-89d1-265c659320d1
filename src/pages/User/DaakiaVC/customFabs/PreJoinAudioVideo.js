import {
  createLocalAudioTrack,
  createLocalVideoTrack,
  facingModeFromLocalTrack,
  Track,
  VideoPresets,
  Mutex,
} from "livekit-client";
import * as React from "react";
import { Modal } from "antd";
import { MdExpandMore } from "react-icons/md";
import {
  // MediaDeviceMenu,
  // TrackToggle,
  useMediaDevices,
  usePersistentUserChoices,
} from "@livekit/components-react";
import { onDeviceError } from "../utils/helper";
import { ParticipantPlaceholder } from "../assets/images/index";
import { TrackToggle } from "../components/TrackToggle";
import "../styles/Prejoin.scss";
// import AudioVideoSettingsModal from "../components/AudioVideoSettingsModal/AudioVideoSettingsModal";
// import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
import AudioSettings from "../components/AudioVideoSettingsModal/AudioSettings";
import VideoSettings from "../components/AudioVideoSettingsModal/VideoSettings";
import PermissionUi from "../components/permissionUi/permissionUi";
import { mediaPermissionManager } from "../utils/mediaPermissionManager";
import { getBrowserInfo } from "../utils/browserDetection";

/**
 * @public
 */
export function usePreviewTracks(options, onError, setToastMessage, setToastStatus, setShowToast) {
  const [tracks, setTracks] = React.useState();
  const [showPermissionModal, setShowPermissionModal] = React.useState(false);
  const [permissionType, setPermissionType] = React.useState('both');
  const [isLoading, setIsLoading] = React.useState(false);
  const [errorInfo, setErrorInfo] = React.useState(null);
  const [fallbackMode, setFallbackMode] = React.useState(null);
  const [userSkipped, setUserSkipped] = React.useState(false);
  const [permissionsChecked, setPermissionsChecked] = React.useState(false);

  const trackLock = React.useMemo(() => new Mutex(), []);
  const browserInfo = React.useMemo(() => getBrowserInfo(), []);

  // Convert MediaStream to LiveKit tracks
  const convertStreamToLivekitTracks = async (stream, requestOptions) => {
    const livekitTracks = [];

    if (requestOptions.audio) {
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length > 0) {
        const audioTrack = await createLocalAudioTrack({
          deviceId: requestOptions.audio.deviceId
        });
        livekitTracks.push(audioTrack);
      }
    }

    if (requestOptions.video) {
      const videoTracks = stream.getVideoTracks();
      if (videoTracks.length > 0) {
        const videoTrack = await createLocalVideoTrack({
          deviceId: requestOptions.video.deviceId,
          resolution: VideoPresets.h720.resolution,
        });
        livekitTracks.push(videoTrack);
      }
    }

    // Stop the original stream since we created new tracks
    stream.getTracks().forEach(track => track.stop());

    return livekitTracks;
  };

  // Handle fallback scenarios when permissions fail
  const handlePermissionFallback = async (originalOptions) => {
    const permissionState = mediaPermissionManager.getPermissionState();

    // Try audio-only fallback
    if (originalOptions.video && originalOptions.audio && permissionState.microphone !== 'denied') {
      try {
        const audioResult = await mediaPermissionManager.requestMediaWithFallback({
          audio: originalOptions.audio
        });
        if (audioResult.success) {
          let livekitTracks;
          if (audioResult.result instanceof MediaStream) {
            livekitTracks = await convertStreamToLivekitTracks(audioResult.result, {
              audio: originalOptions.audio
            });
          } else {
            livekitTracks = audioResult.result;
          }
          setTracks(livekitTracks);
          setFallbackMode('audio-only');
          setToastMessage("Camera access denied. Continuing with microphone only.");
          setToastStatus("warning");
          setShowToast(true);
          return;
        }
      } catch (e) {
        console.log("Audio-only fallback failed:", e);
      }
    }

    // Try video-only fallback
    if (originalOptions.audio && originalOptions.video && permissionState.camera !== 'denied') {
      try {
        const videoResult = await mediaPermissionManager.requestMediaWithFallback({
          video: originalOptions.video
        });
        if (videoResult.success) {
          let livekitTracks;
          if (videoResult.result instanceof MediaStream) {
            livekitTracks = await convertStreamToLivekitTracks(videoResult.result, {
              video: originalOptions.video
            });
          } else {
            livekitTracks = videoResult.result;
          }
          setTracks(livekitTracks);
          setFallbackMode('video-only');
          setToastMessage("Microphone access denied. Continuing with camera only.");
          setToastStatus("warning");
          setShowToast(true);
          return;
        }
      } catch (e) {
        console.log("Video-only fallback failed:", e);
      }
    }

    // All fallbacks failed - show permission modal with guidance
    setShowPermissionModal(true);
  };

  const handlePermissionResponse = async (response) => {
    setShowPermissionModal(false);
    setIsLoading(true);
    setErrorInfo(null);

    try {
      if (response === 'skip') {
        setTracks([]);
        setFallbackMode('no-media');
        setUserSkipped(true); // Mark that user explicitly skipped
        return;
      }

      // Determine what permissions to request based on response
      const requestOptions = {
        audio: response.includes('mic') ? options.audio : false,
        video: response.includes('camera') ? options.video : false,
      };

      // Use the media permission manager for robust permission handling
      const result = await mediaPermissionManager.requestMediaWithFallback(requestOptions);

      if (result.success) {
        // Convert stream/tracks to LiveKit format if needed
        let livekitTracks;
        if (result.result instanceof MediaStream) {
          livekitTracks = await convertStreamToLivekitTracks(result.result, requestOptions);
        } else {
          livekitTracks = result.result;
        }

        setTracks(livekitTracks);
        setFallbackMode(null);
      } else {
        // Handle fallback scenarios
        setErrorInfo(result.errorInfo);
        await handlePermissionFallback(requestOptions);
      }
    } catch (e) {
      console.log("Permission response error:", e);
      setErrorInfo({
        title: 'Unexpected error',
        message: 'An unexpected error occurred while accessing your devices.',
        helpUrl: null
      });

      if (onError && e instanceof Error) {
        onError(e);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Initial permission check and track creation
  const initializeMedia = async () => {
    if (!options.audio && !options.video) {
      setPermissionsChecked(true);
      return;
    }

    setIsLoading(true);
    setErrorInfo(null);

    try {
      // Phase 1: Check existing permissions
      await mediaPermissionManager.checkExistingPermissions(options);
      setPermissionsChecked(true);

      // Phase 2: Determine if we should show permission modal
      const shouldShowModal = mediaPermissionManager.shouldShowPermissionModal(options, userSkipped);

      if (!shouldShowModal) {
        // Phase 3: Try to create tracks directly (permissions already granted or user skipped)
        if (!userSkipped) {
          const result = await mediaPermissionManager.requestMediaWithFallback(options);

          if (result.success) {
            let livekitTracks;
            if (result.result instanceof MediaStream) {
              livekitTracks = await convertStreamToLivekitTracks(result.result, options);
            } else {
              livekitTracks = result.result;
            }
            setTracks(livekitTracks);
          } else {
            // Even with granted permissions, something failed - show modal only if user didn't skip
            if (!userSkipped) {
              setErrorInfo(result.errorInfo);
              const requiredType = mediaPermissionManager.getRequiredPermissionType(options);
              if (requiredType) {
                setPermissionType(requiredType);
                setShowPermissionModal(true);
              }
            }
          }
        }
      } else {
        // Show permission modal for missing permissions only
        const requiredType = mediaPermissionManager.getRequiredPermissionType(options);
        if (requiredType) {
          setPermissionType(requiredType);
          setShowPermissionModal(true);
        }
      }
    } catch (e) {
      console.log("Media initialization error:", e);
      if (!userSkipped) {
        const requiredType = mediaPermissionManager.getRequiredPermissionType(options);
        if (requiredType) {
          setPermissionType(requiredType);
          setShowPermissionModal(true);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    // Reset states when options change (but preserve userSkipped if they explicitly skipped)
    trackLock.lock().then(async (unlock) => {
      try {
        if (options.audio || options.video) {
          // Only reset userSkipped if user is now requesting different permissions
          const currentRequiredType = mediaPermissionManager.getRequiredPermissionType(options);
          if (currentRequiredType && permissionsChecked) {
            // Don't reset userSkipped - let them keep their choice
          }

          await initializeMedia();
        } else {
          // No permissions needed, clear everything
          setTracks([]);
          setShowPermissionModal(false);
          setPermissionsChecked(true);
        }
      } finally {
        unlock();
      }
    });
  }, [JSON.stringify(options), trackLock, permissionsChecked, userSkipped]);

  // Function to retry permissions (useful for manual retry buttons)
  const retryPermissions = () => {
    setUserSkipped(false);
    setErrorInfo(null);
    setFallbackMode(null);
    initializeMedia();
  };

  return {
    tracks,
    showPermissionModal,
    setShowPermissionModal,
    permissionType,
    permissionState: mediaPermissionManager.getPermissionState(),
    handlePermissionResponse,
    isLoading,
    errorInfo,
    fallbackMode,
    browserInfo,
    retryPermissions,
    userSkipped
  };
}

/** @public */
export function usePreviewDevice(enabled, deviceId, deviceKind) {
  const [deviceError, setDeviceError] = React.useState(null);
  const [isCreatingTrack, setIsCreatingTrack] = React.useState(false);

  const devices = useMediaDevices({ kind: deviceKind });
  const [selectedDevice, setSelectedDevice] = React.useState(undefined);

  const [localTrack, setLocalTrack] = React.useState();
  const [localDeviceId, setLocalDeviceId] = React.useState(deviceId);

  React.useEffect(() => {
    setLocalDeviceId(deviceId);
  }, [deviceId]);

  const prevDeviceId = React.useRef(localDeviceId); // Moved prevDeviceId declaration here

  const createTrack = async (id, kind) => {
    try {
      const track =
        kind === "videoinput"
          ? await createLocalVideoTrack({
              deviceId: id,
              resolution: VideoPresets.h720.resolution,
            })
          : await createLocalAudioTrack({ deviceId: id });

      const newDeviceId = await track.getDeviceId();
      if (newDeviceId && id !== newDeviceId) {
        prevDeviceId.current = newDeviceId;
        setLocalDeviceId(newDeviceId);
      }
      setLocalTrack(track);
    } catch (e) {
      if (e instanceof Error) {
        setDeviceError(e);
      }
    }
  };

  const switchDevice = async (track, id) => {
    await track.setDeviceId(id);
    prevDeviceId.current = id;
  };

  React.useEffect(() => {
    if (enabled && !localTrack && !deviceError && !isCreatingTrack) {
      setIsCreatingTrack(true);
      createTrack(localDeviceId, deviceKind).finally(() => {
        setIsCreatingTrack(false);
      });
    }
  }, [
    enabled,
    localTrack,
    deviceError,
    isCreatingTrack,
    localDeviceId,
    deviceKind,
  ]);

  React.useEffect(() => {
    if (!localTrack) {
      return;
    }
    if (!enabled) {
      localTrack.mute().then(() => console.log(localTrack.mediaStreamTrack));
    } else if (
      selectedDevice?.deviceId &&
      prevDeviceId.current !== selectedDevice?.deviceId
    ) {
      switchDevice(localTrack, selectedDevice.deviceId);
    } else {
      localTrack.unmute();
    }
  }, [localTrack, selectedDevice, enabled, deviceKind]);

  React.useEffect(() => {
    return () => {
      if (localTrack) {
        localTrack.stop();
        localTrack.mute();
      }
    };
  }, [localTrack]);

  React.useEffect(() => {
    setSelectedDevice(devices?.find((dev) => dev.deviceId === localDeviceId));
  }, [localDeviceId, devices]);

  return {
    selectedDevice,
    localTrack,
    deviceError,
  };
}

export function PreJoinAudioVideo({
  defaults = {},
  onValidate,
  onError,
  username,
  setIsValid,
  setUserChoices,
  persistUserChoices = true,
  setToastMessage,
  setToastStatus,
  setShowToast,
  setToastNotification,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setDeviceIdAudio,
  ...htmlProps
}) {
  const [isAudioModalOpen, setIsAudioModalOpen] = React.useState(false);
  const [isVideoModalOpen, setIsVideoModalOpen] = React.useState(false);
  const [showPermissionModal, setShowPermissionModal] = React.useState(true);

  const partialDefaults = {
    ...(defaults.audioDeviceId !== undefined && {
      audioDeviceId: defaults.audioDeviceId,
    }),
    ...(defaults.videoDeviceId !== undefined && {
      videoDeviceId: defaults.videoDeviceId,
    }),
    ...(defaults.audioEnabled !== undefined && {
      audioEnabled: defaults.audioEnabled,
    }),
    ...(defaults.videoEnabled !== undefined && {
      videoEnabled: defaults.videoEnabled,
    }),
    ...(defaults.username !== undefined && { username: defaults.username }),
  };

  const {
    userChoices: initialUserChoices,
    saveAudioInputDeviceId,
    saveAudioInputEnabled,
    saveVideoInputDeviceId,
    saveVideoInputEnabled,
    saveUsername,
  } = usePersistentUserChoices({
    defaults: partialDefaults,
    preventSave: !persistUserChoices,
    preventLoad: !persistUserChoices,
  });

  const [audioEnabled, setAudioEnabled] = React.useState(
    initialUserChoices.audioEnabled
  );
  const [videoEnabled, setVideoEnabled] = React.useState(
    initialUserChoices.videoEnabled
  );
  const [audioDeviceId, setAudioDeviceId] = React.useState(
    initialUserChoices.audioDeviceId
  );
  const [videoDeviceId, setVideoDeviceId] = React.useState(
    initialUserChoices.videoDeviceId
  );
  const [speakerDeviceId, setSpeakerDeviceId] = React.useState("");

  React.useEffect(() => {
    saveAudioInputEnabled(audioEnabled);
  }, [audioEnabled, saveAudioInputEnabled]);
  React.useEffect(() => {
    saveVideoInputEnabled(videoEnabled);
  }, [videoEnabled, saveVideoInputEnabled]);
  React.useEffect(() => {
    saveAudioInputDeviceId(audioDeviceId);
  }, [audioDeviceId, saveAudioInputDeviceId]);
  React.useEffect(() => {
    saveVideoInputDeviceId(videoDeviceId);
  }, [videoDeviceId, saveVideoInputDeviceId]);
  React.useEffect(() => {
    saveUsername(username);
  }, [username, saveUsername]);

  const {
    tracks,
    showPermissionModal: showPermissionModalFromHook,
    setShowPermissionModal: setShowPermissionModalFromHook,
    permissionType,
    retryPermissions,
    userSkipped
  } = usePreviewTracks(
    {
      audio: audioEnabled
        ? { deviceId: initialUserChoices.audioDeviceId }
        : false,
      video: videoEnabled
        ? { deviceId: initialUserChoices.videoDeviceId }
        : false,
    },
    onError,
    setToastMessage,
    setToastStatus,
    setShowToast
  );

  const videoEl = React.useRef(null);

  const videoTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Video)[0],
    [tracks]
  );

  const trackFacingMode = React.useMemo(() => {
    if (videoTrack) {
      const { facingMode } = facingModeFromLocalTrack(videoTrack);
      return facingMode;
    } else {
      return "undefined";
    }
  }, [videoTrack]);

  const audioTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Audio)[0],
    [tracks]
  );

  React.useEffect(() => {
    if (videoEl.current && videoTrack) {
      videoTrack.unmute();
      videoTrack.attach(videoEl.current);
    }

    return () => {
      videoTrack?.detach();
    };
  }, [videoTrack]);

  const handleValidation = React.useCallback(
    (values) => {
      if (typeof onValidate === "function") {
        return onValidate(values);
      } else {
        return values.username !== "";
      }
    },
    [onValidate]
  );

  React.useEffect(() => {
    const newUserChoices = {
      username,
      videoEnabled,
      videoDeviceId,
      audioEnabled,
      audioDeviceId,
    };
    setUserChoices(newUserChoices);
    setIsValid(handleValidation(newUserChoices));
  }, [
    username,
    videoEnabled,
    videoDeviceId,
    audioEnabled,
    audioDeviceId,
    handleValidation,
  ]);

  React.useEffect(() => {
    setAudioDeviceId(audioDeviceId);
    setDeviceIdAudio(audioDeviceId);
    setVideoDeviceId(videoDeviceId);
  }, [
    audioDeviceId,
    videoTrack,
    videoDeviceId,
    isVideoModalOpen
  ]);

  // Use the permission modal state from the hook, but allow manual override
  const shouldShowPermissionModal = showPermissionModalFromHook || showPermissionModal;

  const handleClosePermissionModal = () => {
    setShowPermissionModal(false);
    if (setShowPermissionModalFromHook) {
      setShowPermissionModalFromHook(false);
    }
  };

  const handlePermissionsGranted = (deviceInfo) => {
    setAudioEnabled(deviceInfo.audioEnabled);
    setVideoEnabled(deviceInfo.videoEnabled);
    if (deviceInfo.audioDeviceId) {
      setAudioDeviceId(deviceInfo.audioDeviceId);
    }
    if (deviceInfo.videoDeviceId) {
      setVideoDeviceId(deviceInfo.videoDeviceId);
    }
    setShowPermissionModal(false);
    if (setShowPermissionModalFromHook) {
      setShowPermissionModalFromHook(false);
    }
  };

  const handlePermissionsDenied = (error) => {
    console.log("Permissions denied:", error);
    setToastMessage("Permissions denied for accessing audio/video devices.");
    setToastStatus("error");
    setShowToast(true);
    setShowPermissionModal(false);
    if (setShowPermissionModalFromHook) {
      setShowPermissionModalFromHook(false);
    }
  };

  return (
    <>
      <PermissionUi
        open={shouldShowPermissionModal}
        onClose={handleClosePermissionModal}
        onPermissionsGranted={handlePermissionsGranted}
        onPermissionsDenied={handlePermissionsDenied}
        initialPermissionType={permissionType || 'both'}
      />

      <div className="lk-prejoin" {...htmlProps}>
        <div className="lk-video-container" style={{ position: 'relative' }}>
          {videoTrack && (
            <video
              ref={videoEl}
              width="1280"
              height="720"
              data-lk-facing-mode={trackFacingMode}
              className={isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}
            >
              <track kind="captions" />
            </video>
          )}
          {(!videoTrack || !videoEnabled) && (
            <div className="lk-camera-off-note">
              <ParticipantPlaceholder />
            </div>
          )}
          {userSkipped && (
            <div
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                padding: '8px 12px',
                borderRadius: '6px',
                fontSize: '12px',
                zIndex: 10
              }}
            >
              <div className="text-center">
                <div className="mb-1">Devices skipped</div>
                <button
                  className="btn btn-sm btn-outline-light"
                  style={{ fontSize: '10px', padding: '2px 8px' }}
                  onClick={retryPermissions}
                >
                  Enable
                </button>
              </div>
            </div>
          )}
        </div>
        <div
          className="lk-button-group-container"
          style={{ gap: "3rem", justifyContent: "center" }}
        >
          <div
            className="lk-button-group audio"
            style={{ width: "auto", justifyContent: "center" }}
          >
            <TrackToggle
              initialState={audioEnabled}
              source={Track.Source.Microphone}
              showIcon
              onChange={(enabled) => setAudioEnabled(enabled)}
              onDeviceError={onDeviceError}
              className="button-icon"
            />
            <div
              className="lk-button-group-menu"
              onClick={() => {
                setIsAudioModalOpen(!isAudioModalOpen);
                setIsVideoModalOpen(false);
              }}
            >
              <MdExpandMore />
            </div>
          </div>
          <div
            className="lk-button-group video"
            style={{ width: "auto", justifyContent: "center" }}
          >
            <TrackToggle
              initialState={videoEnabled}
              source={Track.Source.Camera}
              showIcon
              onChange={(enabled) => setVideoEnabled(enabled)}
              onDeviceError={onDeviceError}
              className="button-icon"
            />
            <div
              className="lk-button-group-menu"
              onClick={() => {
                setIsAudioModalOpen(false);
                setIsVideoModalOpen(!isVideoModalOpen);
              }}
            >
              <MdExpandMore />
            </div>
          </div>
        </div>

        {/* Audio Video Settings Modal */}
        {/* <AudioVideoSettingsModal
          audio={isAudioModalOpen}
          video={isVideoModalOpen}
          open={isAudioModalOpen || isVideoModalOpen}
          setOpen={isAudioModalOpen ? setIsAudioModalOpen : setIsVideoModalOpen}
          deviceId={isAudioModalOpen ? audioDeviceId : videoDeviceId}
          setDeviceId={isAudioModalOpen ? setAudioDeviceId : setVideoDeviceId}
          track={isAudioModalOpen ? audioTrack : videoTrack}
          setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
          isNoiseCancellationActive={isNoiseCancellationActive}
          setIsNoiseCancellationActive={setIsNoiseCancellationActive}
          isEchoCancellationActive={isEchoCancellationActive}
          setIsEchoCancellationActive={setIsEchoCancellationActive}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
        /> */}

        {/* Audio Settings Modal */}
        <Modal
          open={isAudioModalOpen}
          onCancel={() => setIsAudioModalOpen(false)}
          footer={null}
        >
          <AudioSettings
            deviceId={audioDeviceId}
            setDeviceId={setAudioDeviceId}
            track={audioTrack}
            speakerDeviceId={speakerDeviceId}
            setSpeakerDeviceId={setSpeakerDeviceId}
            room={room}
          />
        </Modal>

        {/* Video Settings Modal */}
        <Modal
          open={isVideoModalOpen}
          onCancel={() => setIsVideoModalOpen(false)}
          footer={null}
        >
          <VideoSettings
            deviceId={videoDeviceId}
            setDeviceId={setVideoDeviceId}
            track={videoTrack}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            // setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
            open={isVideoModalOpen}
            trackFacingMode={trackFacingMode}
          />
        </Modal>

        {/* Virtual Background Modal */}
        {/* <VirtualBackgroundModal
          open={isVisualEffectsModalOpen}
          setOpen={setIsVisualEffectsModalOpen}
          backgrounds={backgrounds}
          setBackgrounds={setBackgrounds}
          room={room}
        /> */}
      </div>
    </>
  );
}