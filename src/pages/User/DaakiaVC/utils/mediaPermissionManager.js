import { createLocalTracks } from "livekit-client";
import {
  getBrowserInfo,
  getPermissionStrategy,
  supportsPermissionsAPI,
  getBrowserSpecificErrorMessage
} from './browserDetection';

/**
 * Media Permission Manager - Handles browser-specific permission flows
 */
export class MediaPermissionManager {
  constructor() {
    this.browser = getBrowserInfo();
    this.strategy = getPermissionStrategy();
    this.permissionState = {
      camera: 'unknown',
      microphone: 'unknown'
    };
  }

  /**
   * Check existing permissions without triggering prompts
   */
  async checkExistingPermissions(options = {}) {
    if (!supportsPermissionsAPI()) {
      return this.permissionState;
    }

    try {
      if (options.video) {
        const cameraResult = await navigator.permissions.query({ name: 'camera' });
        this.permissionState.camera = cameraResult.state;
      }

      if (options.audio) {
        const micResult = await navigator.permissions.query({ name: 'microphone' });
        this.permissionState.microphone = micResult.state;
      }
    } catch (error) {
      console.log('Error checking permissions:', error);
    }

    return this.permissionState;
  }

  /**
   * Determine if we should show permission modal
   */
  shouldShowPermissionModal(options) {
    const { audio, video } = options;

    // If no permissions requested, don't show modal
    if (!audio && !video) return false;

    // If any requested permission is denied, show modal with guidance
    if ((audio && this.permissionState.microphone === 'denied') ||
        (video && this.permissionState.camera === 'denied')) {
      return true;
    }

    // If all requested permissions are granted, don't show modal
    if ((!audio || this.permissionState.microphone === 'granted') &&
        (!video || this.permissionState.camera === 'granted')) {
      return false;
    }

    // For unknown/prompt state, show modal
    return true;
  }

  /**
   * Browser-specific getUserMedia implementation
   */
  async getUserMediaBrowserSpecific(constraints) {
    switch (this.strategy) {
      case 'sequential':
        return this.getMediaSequential(constraints);
      case 'simultaneous':
        return this.getMediaSimultaneous(constraints);
      case 'user-gesture':
        return this.getMediaWithUserGesture(constraints);
      default:
        return this.getMediaFallback(constraints);
    }
  }

  /**
   * Sequential media requests (Firefox)
   */
  async getMediaSequential(constraints) {
    const streams = [];

    try {
      // Request audio first
      if (constraints.audio) {
        const audioStream = await navigator.mediaDevices.getUserMedia({
          audio: constraints.audio,
          video: false
        });
        streams.push(audioStream);
        this.permissionState.microphone = 'granted';
      }

      // Then request video
      if (constraints.video) {
        const videoStream = await navigator.mediaDevices.getUserMedia({
          audio: false,
          video: constraints.video
        });
        streams.push(videoStream);
        this.permissionState.camera = 'granted';
      }

      // Combine streams
      if (streams.length > 1) {
        const combinedStream = new MediaStream();
        streams.forEach(stream => {
          stream.getTracks().forEach(track => {
            combinedStream.addTrack(track);
          });
        });
        return combinedStream;
      }

      return streams[0];
    } catch (error) {
      // Clean up any successful streams
      streams.forEach(stream => {
        stream.getTracks().forEach(track => track.stop());
      });
      throw error;
    }
  }

  /**
   * Simultaneous media requests (Chrome/Edge)
   */
  async getMediaSimultaneous(constraints) {
    const stream = await navigator.mediaDevices.getUserMedia(constraints);

    if (constraints.audio) this.permissionState.microphone = 'granted';
    if (constraints.video) this.permissionState.camera = 'granted';

    return stream;
  }

  /**
   * User gesture required requests (Safari)
   */
  async getMediaWithUserGesture(constraints) {
    // Safari requires user gesture - this should only be called from user interaction
    return this.getMediaSimultaneous(constraints);
  }

  /**
   * Fallback to createLocalTracks
   */
  async getMediaFallback(constraints) {
    const trackOptions = {
      audio: constraints.audio || false,
      video: constraints.video || false
    };

    const tracks = await createLocalTracks(trackOptions);

    if (constraints.audio) this.permissionState.microphone = 'granted';
    if (constraints.video) this.permissionState.camera = 'granted';

    return tracks;
  }

  /**
   * Progressive fallback strategy
   */
  async requestMediaWithFallback(options) {
    const constraints = {
      audio: options.audio || false,
      video: options.video || false
    };

    const fallbackStrategies = [
      // Strategy 1: Try both audio and video
      () => this.getUserMediaBrowserSpecific(constraints),

      // Strategy 2: Try audio only
      () => constraints.audio ? this.getUserMediaBrowserSpecific({ audio: constraints.audio }) : null,

      // Strategy 3: Try video only
      () => constraints.video ? this.getUserMediaBrowserSpecific({ video: constraints.video }) : null,

      // Strategy 4: Fallback to createLocalTracks
      () => this.getMediaFallback(constraints)
    ];

    let lastError = null;

    // Try each strategy sequentially
    for (let i = 0; i < fallbackStrategies.length; i += 1) {
      const strategy = fallbackStrategies[i];
      if (!strategy) {
        // eslint-disable-next-line no-continue
        continue;
      }

      try {
        // eslint-disable-next-line no-await-in-loop
        const result = await strategy();
        return { success: true, result, error: null };
      } catch (error) {
        lastError = error;
        console.log('Media request strategy failed:', error);

        // Update permission states on error
        if (error.name === 'NotAllowedError') {
          if (constraints.audio) this.permissionState.microphone = 'denied';
          if (constraints.video) this.permissionState.camera = 'denied';
        }
      }
    }

    return {
      success: false,
      result: null,
      error: lastError,
      errorInfo: getBrowserSpecificErrorMessage(lastError, this.browser)
    };
  }

  /**
   * Get current permission state
   */
  getPermissionState() {
    return { ...this.permissionState };
  }

  /**
   * Reset permission state
   */
  resetPermissionState() {
    this.permissionState = {
      camera: 'unknown',
      microphone: 'unknown'
    };
  }
}

// Singleton instance
export const mediaPermissionManager = new MediaPermissionManager();
