/**
 * Browser detection utilities for media permission handling
 */

export const getBrowserInfo = () => {
  const { userAgent } = navigator;
  const isChrome = /Chrome/.test(userAgent) && !/Edg/.test(userAgent) && !/OPR/.test(userAgent);
  const isFirefox = /Firefox/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  const isEdge = /Edg/.test(userAgent);
  const isOpera = /OPR/.test(userAgent);

  return {
    isChrome,
    isFirefox,
    isSafari,
    isEdge,
    isOpera,
    userAgent
  };
};

export const isHTTPS = () => {
  return window.location.protocol === 'https:' || window.location.hostname === 'localhost';
};

export const supportsPermissionsAPI = () => {
  return 'permissions' in navigator && 'query' in navigator.permissions;
};

export const supportsGetUserMedia = () => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
};

/**
 * Check if the current environment supports media permissions
 */
export const canRequestMediaPermissions = () => {
  const browser = getBrowserInfo();

  // Safari requires HTTPS for media permissions
  if (browser.isSafari && !isHTTPS()) {
    return false;
  }

  return supportsGetUserMedia();
};

/**
 * Get browser-specific permission request strategy
 */
export const getPermissionStrategy = () => {
  const browser = getBrowserInfo();

  if (browser.isFirefox) {
    return 'sequential'; // Firefox handles sequential requests better
  } else if (browser.isSafari) {
    return 'user-gesture'; // Safari requires user gesture
  } else if (browser.isChrome || browser.isEdge) {
    return 'simultaneous'; // Chrome/Edge can handle simultaneous requests
  }

  return 'fallback'; // Use createLocalTracks as fallback
};

/**
 * Get browser-specific error messages
 */
export const getBrowserSpecificErrorMessage = (error, browser = getBrowserInfo()) => {
  const errorName = error.name || error.message || '';

  if (errorName.includes('NotAllowedError') || errorName.includes('Permission denied')) {
    if (browser.isChrome || browser.isEdge) {
      return {
        title: 'Camera and microphone access blocked',
        message: 'Click the camera icon in your address bar and select "Allow" to enable access.',
        helpUrl: 'https://support.google.com/chrome/answer/2693767'
      };
    } else if (browser.isFirefox) {
      return {
        title: 'Camera and microphone access blocked',
        message: 'Click the shield icon in your address bar and select "Allow" for camera and microphone.',
        helpUrl: 'https://support.mozilla.org/en-US/kb/how-manage-your-camera-and-microphone-permissions'
      };
    } else if (browser.isSafari) {
      return {
        title: 'Camera and microphone access blocked',
        message: 'Go to Safari > Settings > Websites > Camera/Microphone and allow access for this site.',
        helpUrl: 'https://support.apple.com/guide/safari/websites-ibrwe2159f50/mac'
      };
    }
  } else if (errorName.includes('NotFoundError') || errorName.includes('DevicesNotFoundError')) {
    return {
      title: 'No camera or microphone found',
      message: 'Please connect a camera and microphone to your device and try again.',
      helpUrl: null
    };
  } else if (errorName.includes('NotReadableError') || errorName.includes('TrackStartError')) {
    return {
      title: 'Camera or microphone is busy',
      message: 'Your camera or microphone might be in use by another application. Please close other apps and try again.',
      helpUrl: null
    };
  }

  return {
    title: 'Unable to access camera and microphone',
    message: 'Please check your browser settings and try again.',
    helpUrl: null
  };
};
