import React, { 
  useEffect, 
  // useRef, 
  useState,
  useRef 
} from "react";
import "./AudioVideoSettingsModal.scss";
import { FaAngleRight, FaAngleDown, FaCheck } from "react-icons/fa";
import { ReactComponent as CameraIcon } from "./Assets/cameraDevice.svg";
// import { ReactComponent as VisualEffectsIcon } from "./Assets/visualEffects.svg";
import { ReactComponent as MirrorSelfIcon } from "./Assets/mirrorSelf.svg";
import Switch from "../../../../../components/Antd/Switch/index.ant";
import { onDeviceError } from "../../utils/helper";

export default function VideoSettings({
  deviceId,
  setDeviceId,
  // track, // Removed unused prop
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  // setIsVisualEffectsModalOpen,
}) {
  const [videoDeviceName, setVideoDeviceName] = useState("Unknown Device");
  const [availableDevices, setAvailableDevices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  // const [videoStream, setVideoStream] = useState(null);
  // const videoRef = useRef(null);

  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(
          (device) => device.kind === "videoinput"
        );
        setAvailableDevices(videoDevices);

        const currentDevice = videoDevices.find(
          (device) => device.deviceId === deviceId
        );
        if (currentDevice) {
          setVideoDeviceName(currentDevice.label);
          setDeviceId(currentDevice.deviceId);
        }
      } catch (error) {
        console.error("Error fetching devices:", error);
        onDeviceError(error);
      }
    };

    fetchDevices();
  }, [deviceId]);

  const handleCameraChange = async (newDeviceId) => {
    // If selecting the same device, just close the dropdown
    if (newDeviceId === deviceId) {
      setIsDropdownOpen(false);
      return;
    }

    setIsLoading(true);
    try {
      setDeviceId(newDeviceId);
      const selectedDevice = availableDevices.find(
        (device) => device.deviceId === newDeviceId
      );
      if (selectedDevice) {
        setVideoDeviceName(selectedDevice.label);
      }
      setIsDropdownOpen(false);
    } catch (error) {
      console.error("Error switching camera:", error);
      onDeviceError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // useEffect(() => {
  //   const enableCamera = async () => {
  //     if (!deviceId) return;

  //     if (videoStream) {
  //       videoStream.getTracks().forEach((tracks) => tracks.stop());
  //     }

  //     try {
  //       const stream = await navigator.mediaDevices.getUserMedia({
  //         video: { deviceId: { exact: deviceId } },
  //       });
  //       setVideoStream(stream);
  //       if (videoRef.current) videoRef.current.srcObject = stream;
  //     } catch (error) {
  //       console.error("Error enabling camera:", error);
  //     }
  //   };

  //   enableCamera();

  //   return () => {
  //     if (videoStream) {
  //       videoStream.getTracks().forEach((tracks) => tracks.stop());
  //     }
  //   };
  // }, [deviceId]);

  const toggleDropdown = () => {
    if (!isLoading) {
      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  return (
    <div className="audio-video-settings-modal-options">
      <h4>Video Settings</h4>

      {/* Live Camera preview here */}
      {/* <div className="lk-video-container">
        <video
          ref={videoRef}
          width="100%"
          height="250"
          data-lk-facing-mode={trackFacingMode}
          autoPlay
          className={
            isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"
          }
        >
          <track kind="captions" />
        </video>
      </div> */}

      {/* Camera Device Selection */}
      <div className="audio-video-settings-modal-option">
        <CameraIcon className="audio-video-settings-modal-icon" />
        <div 
          className="audio-video-settings-modal-option-dropdown device-options-mic"
          ref={dropdownRef}
        >
          {/* Custom dropdown trigger */}
          <div 
            className="audio-video-settings-modal-device-options custom-dropdown-trigger"
            onClick={toggleDropdown}
          >
            {isLoading ? (
              "Switching camera..."
            ) : (
              <>
                {videoDeviceName.length > 30
                  ? `${videoDeviceName.slice(0, 30)}...`
                  : videoDeviceName}
                {isDropdownOpen ? <FaAngleDown /> : <FaAngleRight />}
              </>
            )}
          </div>

          {/* Custom dropdown menu */}
          {isDropdownOpen && (
            <div className="custom-dropdown-menu">
              {availableDevices.map((device) => (
                <div
                  key={device.deviceId}
                  className="custom-dropdown-item"
                  onClick={() => handleCameraChange(device.deviceId)}
                >
                  <span>
                    {device.label.length > 30
                      ? `${device.label.slice(0, 30)}...`
                      : device.label}
                  </span>
                  {device.deviceId === deviceId && <FaCheck className="selected-icon" />}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Visual Effects */}
      {/* <div className="audio-video-settings-modal-option">
        <VisualEffectsIcon className="audio-video-settings-modal-icon" />
        <div
          className="audio-video-settings-modal-option-switch visual-effects"
          onClick={() => setIsVisualEffectsModalOpen(true)}
        >
          <span className="device-name">Apply Visual Effects</span>
          <FaAngleRight />
        </div>
      </div> */}

      <div className="audio-video-settings-modal-option">
        <MirrorSelfIcon className="audio-video-settings-modal-icon" />
        <div className="audio-video-settings-modal-option-switch">
          <span className="device-name">Mirror Self View</span>
          <Switch
            className="audio-video-settings-modal-switch"
            checked={isSelfVideoMirrored}
            onChange={() => setIsSelfVideoMirrored(!isSelfVideoMirrored)}
          />
        </div>
      </div>
    </div>
  );
}
