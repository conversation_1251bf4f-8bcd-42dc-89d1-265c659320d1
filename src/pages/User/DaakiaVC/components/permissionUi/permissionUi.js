import React from "react";
import { Modal } from "antd";
import "./permissionUi.scss";
import PermissionUiImage from "../../customFabs/icons/premissionUiImage.svg";
import PermissionUiMicImage from "../../customFabs/icons/permissionUiMic.svg";

export default function PermissionUi({ open, onClose, onAllow, permissionType = 'both' }) {
  const getImage = () => {
    switch(permissionType) {
      case 'mic':
        return PermissionUiMicImage;
      case 'camera':
      case 'both':
      default:
        return PermissionUiImage;
    }
  };

  const getTitle = () => {
    switch(permissionType) {
      case 'mic':
        return "Enable your microphone for the meeting";
      case 'camera':
        return "Enable your camera for the meeting";
      case 'both':
      default:
        return "Enable your camera for the meeting";
    }
  };

  const getDescription = () => {
    switch(permissionType) {
      case 'mic':
        return "You can still turn it off anytime in the meeting";
      case 'camera':
      case 'both':
      default:
        return "You can still turn it off anytime in the meeting";
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={600}
      closable={false}
      maskClosable={false}
      className="permission-ui-modal"
      bodyStyle={{ padding: 0, overflow: 'hidden', minHeight: '420px' }}
      style={{ maxWidth: '600px', height: 'auto' }}
      maskStyle={{
        backgroundColor: "rgba(0, 0, 0, 0.55)",
        backdropFilter: "blur(10px)",
      }}
    >
      <div className="permission-ui-container container text-center p-0">
        <img src={getImage()} alt="Enable camera and mic" className="permission-ui-illustration" />
        <h2 className="permission-ui-title mb-2">{getTitle()}</h2>
        <p className="permission-ui-desc mb-4">{getDescription()}</p>
        <div className="d-flex flex-column align-items-center w-100">
          {permissionType === 'both' && (
            <>
              <button className="btn btn-outline-primary w-100 permission-ui-btn" onClick={() => onAllow('mic_camera')}>Turn on Mic and Camera</button>
              <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
            </>
          )}
          {permissionType === 'camera' && (
            <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
          )}
          {permissionType === 'mic' && (
            <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('mic')}>Turn on Microphone</button>
          )}
          <button className="btn btn-link permission-ui-link" onClick={() => onAllow('skip')}>Continue without using microphone and camera</button>
        </div>
      </div>
    </Modal>
  );
}
