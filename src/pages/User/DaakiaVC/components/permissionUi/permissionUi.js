import React from "react";
import { Modal } from "antd";
import "./permissionUi.scss";
import PermissionUiImage from "../../customFabs/icons/premissionUiImage.svg";
import PermissionUiMicImage from "../../customFabs/icons/permissionUiMic.svg";

export default function PermissionUi({
  open,
  onClose,
  onAllow,
  permissionType = 'both',
  permissionState = { camera: 'unknown', microphone: 'unknown' }
}) {
  const getImage = () => {
    switch(permissionType) {
      case 'mic':
        return PermissionUiMicImage;
      case 'camera':
      case 'both':
      default:
        return PermissionUiImage;
    }
  };

  const getTitle = () => {
    // If permissions are blocked, show a different title
    if (isPermissionBlocked()) {
      const blockedPermission = getBlockedPermission();
      if (blockedPermission === 'both') {
        return "Camera and microphone access is blocked";
      } else if (blockedPermission === 'camera') {
        return "Camera access is blocked";
      } else if (blockedPermission === 'microphone') {
        return "Microphone access is blocked";
      }
    }

    // Default titles for permission requests
    switch(permissionType) {
      case 'mic':
        return "Enable your microphone for the meeting";
      case 'camera':
        return "Enable your camera for the meeting";
      case 'both':
      default:
        return "Enable your camera and microphone for the meeting";
    }
  };

  const getDescription = () => {
    // Check if any permissions are denied at browser level
    const blockedPermission = getBlockedPermission();

    if (isPermissionBlocked()) {
      if (blockedPermission === 'both') {
        return "You'll need to allow access to camera and microphone in your browser settings";
      } else if (blockedPermission === 'camera') {
        return "You'll need to allow camera access in your browser settings";
      } else if (blockedPermission === 'microphone') {
        return "You'll need to allow microphone access in your browser settings";
      }
    }

    // Default descriptions
    switch(permissionType) {
      case 'mic':
        return "You can still turn it off anytime during the meeting";
      case 'camera':
        return "You can still turn it off anytime during the meeting";
      case 'both':
      default:
        return "You can still turn them off anytime during the meeting";
    }
  };

  // Check if browser permissions are blocked
  const isPermissionBlocked = () => {
    if (permissionType === 'both') {
      // For 'both', check if either camera or mic is denied
      return permissionState.camera === 'denied' || permissionState.microphone === 'denied';
    } else if (permissionType === 'camera') {
      return permissionState.camera === 'denied';
    } else if (permissionType === 'mic') {
      return permissionState.microphone === 'denied';
    }
    return false;
  };

  // Get the specific permission that's blocked (for better UI guidance)
  const getBlockedPermission = () => {
    if (permissionType === 'both') {
      if (permissionState.camera === 'denied' && permissionState.microphone === 'denied') {
        return 'both';
      } else if (permissionState.camera === 'denied') {
        return 'camera';
      } else if (permissionState.microphone === 'denied') {
        return 'microphone';
      }
    }
    return permissionType;
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={600}
      closable={false}
      maskClosable={false}
      className="permission-ui-modal"
      bodyStyle={{ padding: 0, overflow: 'hidden', minHeight: '420px' }}
      style={{ maxWidth: '600px', height: 'auto' }}
      maskStyle={{
        backgroundColor: "rgba(0, 0, 0, 0.55)",
        backdropFilter: "blur(10px)",
      }}
    >
      <div className="permission-ui-container container text-center p-0">
        <img src={getImage()} alt="Enable camera and mic" className="permission-ui-illustration" />
        <h2 className="permission-ui-title mb-2">{getTitle()}</h2>
        <p className="permission-ui-desc mb-4">{getDescription()}</p>
        <div className="d-flex flex-column align-items-center w-100">
          {isPermissionBlocked() ? (
            // Show browser settings guidance if permissions are blocked
            <>
              <button
                className="btn btn-primary w-100 permission-ui-btn"
                onClick={() => {
                  // Open browser settings instructions in a new tab
                  window.open('https://support.google.com/chrome/answer/114662?hl=en&co=GENIE.Platform%3DDesktop', '_blank');
                }}
              >
                Open Browser Settings Help
              </button>
              <button className="btn btn-link permission-ui-link" onClick={() => onAllow('skip')}>
                {getBlockedPermission() === 'both'
                  ? 'Continue without using microphone and camera'
                  : `Continue without using ${getBlockedPermission()}`}
              </button>
            </>
          ) : (
            // Normal permission flow
            <>
              {permissionType === 'both' && (
                <>
                  <button className="btn btn-outline-primary w-100 permission-ui-btn" onClick={() => onAllow('mic_camera')}>Turn on Mic and Camera</button>
                  <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
                </>
              )}
              {permissionType === 'camera' && (
                <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
              )}
              {permissionType === 'mic' && (
                <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('mic')}>Turn on Microphone</button>
              )}
              <button className="btn btn-link permission-ui-link" onClick={() => onAllow('skip')}>
                Continue without using {permissionType === 'both' ? 'microphone and camera' : permissionType === 'mic' ? 'microphone' : 'camera'}
              </button>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}
