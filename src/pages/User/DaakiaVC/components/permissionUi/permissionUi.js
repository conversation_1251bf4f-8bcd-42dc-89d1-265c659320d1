import React, { useState } from "react";
import { Modal } from "antd";
import { Track } from "livekit-client";
import "./permissionUi.scss";
import PermissionUiImage from "../../customFabs/icons/premissionUiImage.svg";
import PermissionUiMicImage from "../../customFabs/icons/permissionUiMic.svg";
import { mediaPermissionManager } from "../../utils/mediaPermissionManager";

export default function PermissionUi({
  open,
  onClose,
  onPermissionsGranted,
  onPermissionsDenied,
  initialPermissionType = 'both'
}) {
  const [permissionType] = useState(initialPermissionType);
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(false);
  const [error, setError] = useState(null);

  const checkPermissions = async (type) => {
    setIsCheckingPermissions(true);
    setError(null);

    try {
      const options = {
        audio: type === 'mic' || type === 'mic_camera' ? { deviceId: undefined } : false,
        video: type === 'camera' || type === 'mic_camera' ? { deviceId: undefined } : false
      };

      // Use the enhanced media permission manager for robust permission handling
      const result = await mediaPermissionManager.requestMediaWithFallback(options);

      if (result.success) {
        let audioDeviceId;
        let videoDeviceId;
        let audioEnabled = false;
        let videoEnabled = false;

        if (result.result instanceof Array) {
          // LiveKit tracks
          const audioTrack = result.result.find(track => track.kind === Track.Kind.Audio);
          const videoTrack = result.result.find(track => track.kind === Track.Kind.Video);

          audioDeviceId = audioTrack?.getDeviceId();
          videoDeviceId = videoTrack?.getDeviceId();
          audioEnabled = !!audioTrack;
          videoEnabled = !!videoTrack;

          // Stop tracks after getting device IDs
          result.result.forEach(track => track.stop());
        } else if (result.result instanceof MediaStream) {
          // MediaStream
          const audioTracks = result.result.getAudioTracks();
          const videoTracks = result.result.getVideoTracks();

          audioEnabled = audioTracks.length > 0;
          videoEnabled = videoTracks.length > 0;

          // Get device IDs from tracks
          if (audioTracks.length > 0) {
            audioDeviceId = audioTracks[0].getSettings().deviceId;
          }
          if (videoTracks.length > 0) {
            videoDeviceId = videoTracks[0].getSettings().deviceId;
          }

          // Stop the stream
          result.result.getTracks().forEach(track => track.stop());
        }

        const deviceIds = {
          audioDeviceId,
          videoDeviceId,
          audioEnabled,
          videoEnabled
        };

        onPermissionsGranted(deviceIds);
        onClose();
      } else {
        // Handle permission errors with browser-specific guidance
        let errorMessage = result.errorInfo?.message || 'Permission denied for accessing audio/video devices.';

        if (result.errorInfo?.title) {
          errorMessage = `${result.errorInfo.title}: ${errorMessage}`;
        }

        setError(errorMessage);
        onPermissionsDenied?.(new Error(errorMessage));
      }
    } catch (err) {
      console.log("Permission check error:", err);
      setError(err.message || 'An unexpected error occurred while accessing your devices.');
      onPermissionsDenied?.(err);
    } finally {
      setIsCheckingPermissions(false);
    }
  };

  const handleAllow = (type) => {
    if (type === 'skip') {
      onPermissionsGranted({
        audioEnabled: false,
        videoEnabled: false
      });
      onClose();
      return;
    }

    checkPermissions(type);
  };

  const getImage = () => {
    switch(permissionType) {
      case 'mic':
        return PermissionUiMicImage;
      case 'camera':
      case 'both':
      default:
        return PermissionUiImage;
    }
  };

  const getTitle = () => {
    switch(permissionType) {
      case 'mic':
        return "Enable your microphone for the meeting";
      case 'camera':
        return "Enable your camera for the meeting";
      case 'both':
      default:
        return "Enable your camera for the meeting";
    }
  };

  const getDescription = () => {
    switch(permissionType) {
      case 'mic':
        return "You can still turn it off anytime in the meeting";
      case 'camera':
      case 'both':
      default:
        return "You can still turn it off anytime in the meeting";
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={600}
      closable={false}
      maskClosable={false}
      className="permission-ui-modal"
      bodyStyle={{ padding: 0, overflow: 'hidden', minHeight: '420px' }}
      style={{ maxWidth: '600px', height: 'auto' }}
      maskStyle={{
        backgroundColor: "rgba(0, 0, 0, 0.55)",
        backdropFilter: "blur(10px)",
      }}
    >
      <div className="permission-ui-container container text-center p-0">
        <img src={getImage()} alt="Enable camera and mic" className="permission-ui-illustration" />
        <h2 className="permission-ui-title mb-2">{getTitle()}</h2>
        <p className="permission-ui-desc mb-4">{getDescription()}</p>
        {error && <p className="text-danger mb-3">{error}</p>}
        <div className="d-flex flex-column align-items-center w-100">
          {isCheckingPermissions ? (
            <div className="text-center">
              <div className="spinner-border text-primary mb-3" role="status">
                <span className="sr-only">Checking permissions...</span>
              </div>
              <p>Checking device permissions...</p>
            </div>
          ) : (
            <>
              {permissionType === 'both' && (
                <>
                  <button
                    className="btn btn-outline-primary w-100 permission-ui-btn"
                    onClick={() => handleAllow('mic_camera')}
                    disabled={isCheckingPermissions}
                  >
                    Turn on Mic and Camera
                  </button>
                  <button
                    className="btn btn-primary w-100 permission-ui-btn"
                    onClick={() => handleAllow('camera')}
                    disabled={isCheckingPermissions}
                  >
                    Turn on Camera
                  </button>
                </>
              )}
              {permissionType === 'camera' && (
                <button
                  className="btn btn-primary w-100 permission-ui-btn"
                  onClick={() => handleAllow('camera')}
                  disabled={isCheckingPermissions}
                >
                  Turn on Camera
                </button>
              )}
              {permissionType === 'mic' && (
                <button
                  className="btn btn-primary w-100 permission-ui-btn"
                  onClick={() => handleAllow('mic')}
                  disabled={isCheckingPermissions}
                >
                  Turn on Microphone
                </button>
              )}
              <button
                className="btn btn-link permission-ui-link"
                onClick={() => handleAllow('skip')}
                disabled={isCheckingPermissions}
              >
                Continue without using microphone and camera
              </button>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}
