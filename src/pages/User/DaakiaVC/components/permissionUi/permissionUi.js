import React from "react";
import { Modal } from "antd";
import "./permissionUi.scss";
import PermissionUiImage from "../../customFabs/icons/premissionUiImage.svg";
import PermissionUiMicImage from "../../customFabs/icons/permissionUiMic.svg";

export default function PermissionUi({
  open,
  onClose,
  onAllow,
  permissionType = 'both',
  permissionState = { camera: 'unknown', microphone: 'unknown' }
}) {
  const getImage = () => {
    switch(permissionType) {
      case 'mic':
        return PermissionUiMicImage;
      case 'camera':
      case 'both':
      default:
        return PermissionUiImage;
    }
  };

  const getTitle = () => {
    switch(permissionType) {
      case 'mic':
        return "Enable your microphone for the meeting";
      case 'camera':
        return "Enable your camera for the meeting";
      case 'both':
      default:
        return "Enable your camera and microphone for the meeting";
    }
  };

  const getDescription = () => {
    // Check if any permissions are denied at browser level
    if (permissionType === 'both' &&
        (permissionState.camera === 'denied' || permissionState.microphone === 'denied')) {
      return "You'll need to allow access in your browser settings to enable these devices";
    } else if (permissionType === 'camera' && permissionState.camera === 'denied') {
      return "You'll need to allow camera access in your browser settings";
    } else if (permissionType === 'mic' && permissionState.microphone === 'denied') {
      return "You'll need to allow microphone access in your browser settings";
    }

    // Default descriptions
    switch(permissionType) {
      case 'mic':
        return "You can still turn it off anytime during the meeting";
      case 'camera':
        return "You can still turn it off anytime during the meeting";
      case 'both':
      default:
        return "You can still turn them off anytime during the meeting";
    }
  };

  // Check if browser permissions are blocked
  const isPermissionBlocked = () => {
    if (permissionType === 'both') {
      return permissionState.camera === 'denied' || permissionState.microphone === 'denied';
    } else if (permissionType === 'camera') {
      return permissionState.camera === 'denied';
    } else if (permissionType === 'mic') {
      return permissionState.microphone === 'denied';
    }
    return false;
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={600}
      closable={false}
      maskClosable={false}
      className="permission-ui-modal"
      bodyStyle={{ padding: 0, overflow: 'hidden', minHeight: '420px' }}
      style={{ maxWidth: '600px', height: 'auto' }}
      maskStyle={{
        backgroundColor: "rgba(0, 0, 0, 0.55)",
        backdropFilter: "blur(10px)",
      }}
    >
      <div className="permission-ui-container container text-center p-0">
        <img src={getImage()} alt="Enable camera and mic" className="permission-ui-illustration" />
        <h2 className="permission-ui-title mb-2">{getTitle()}</h2>
        <p className="permission-ui-desc mb-4">{getDescription()}</p>
        <div className="d-flex flex-column align-items-center w-100">
          {isPermissionBlocked() ? (
            // Show browser settings guidance if permissions are blocked
            <>
              <button
                className="btn btn-primary w-100 permission-ui-btn"
                onClick={() => {
                  // Open browser settings instructions in a new tab
                  window.open('https://support.google.com/chrome/answer/114662?hl=en&co=GENIE.Platform%3DDesktop', '_blank');
                }}
              >
                Open Browser Settings Help
              </button>
              <button className="btn btn-link permission-ui-link" onClick={() => onAllow('skip')}>
                Continue without using {permissionType === 'both' ? 'microphone and camera' : permissionType === 'mic' ? 'microphone' : 'camera'}
              </button>
            </>
          ) : (
            // Normal permission flow
            <>
              {permissionType === 'both' && (
                <>
                  <button className="btn btn-outline-primary w-100 permission-ui-btn" onClick={() => onAllow('mic_camera')}>Turn on Mic and Camera</button>
                  <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
                </>
              )}
              {permissionType === 'camera' && (
                <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
              )}
              {permissionType === 'mic' && (
                <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('mic')}>Turn on Microphone</button>
              )}
              <button className="btn btn-link permission-ui-link" onClick={() => onAllow('skip')}>
                Continue without using {permissionType === 'both' ? 'microphone and camera' : permissionType === 'mic' ? 'microphone' : 'camera'}
              </button>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}
